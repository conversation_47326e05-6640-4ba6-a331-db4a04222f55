import os
import requests
from dotenv import load_dotenv

def fetch_erpnext_items():
    """
    Connects to the ERPNext API and fetches a list of all items.
    """
    # Load environment variables from the .env file
    load_dotenv()

    # Get credentials from environment variables
    api_url = os.getenv("ERPNEXT_API_URL")
    api_key = os.getenv("ERPNEXT_API_KEY")
    api_secret = os.getenv("ERPNEXT_API_SECRET")

    # Validate that all required environment variables are set
    if not all([api_url, api_key, api_secret]):
        print("Error: Missing one or more required environment variables.")
        print("Please check your .env file for: ERPNEXT_API_URL, ERPNEXT_API_KEY, ERPNEXT_API_SECRET")
        return

    # Define the API endpoint and headers
    item_endpoint = f"{api_url}/api/resource/Item"
    headers = {
        "Authorization": f"token {api_key}:{api_secret}",
        "Accept": "application/json",
    }
    
    # Define the parameters to fetch specific fields and all records
    params = {
        "fields": '["item_code", "item_name", "item_group"]',
        "limit_page_length": 0
    }

    try:
        print(f"Attempting to fetch items from {api_url}...")
        response = requests.get(item_endpoint, headers=headers, params=params)
        
        # Raise an exception for bad status codes (4xx or 5xx)
        response.raise_for_status()
        
        items = response.json().get("data", [])

        if not items:
            print("Successfully connected, but no items were found.")
        else:
            print("\n--- ✅ Success! Found the following items: ---")
            for item in items:
                print(f"- Code: {item['item_code']}, Name: {item['item_name']}, Group: {item['item_group']}")
            print("--------------------------------------------\n")

    except requests.exceptions.HTTPError as e:
        print(f"\n--- ❌ HTTP Error ---")
        print(f"Status Code: {e.response.status_code}")
        print(f"Response: {e.response.text}")
        print("---------------------\n")
    except requests.exceptions.RequestException as e:
        print(f"\n--- ❌ Connection Error ---")
        print(f"An error occurred: {e}")
        print("Please check your ERPNEXT_API_URL and network connection.")
        print("---------------------------\n")

if __name__ == "__main__":
    fetch_erpnext_items()