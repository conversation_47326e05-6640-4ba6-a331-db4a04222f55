# Sales Order Task Generator

## Overview

The Sales Order Task Generator (`sales_order_tasks.py`) is a comprehensive script that fetches submitted sales orders from ERPNext, allows interactive selection, and automatically generates detailed tasks based on the order items. This tool follows the same high standards and patterns established by `quotegen.py`.

## Features

- **Environment Selection**: Choose between Staging and Production ERPNext environments
- **Smart Order Filtering**: Fetches only submitted sales orders within a specified time range
- **Interactive Selection**: User-friendly interface to browse and select from available orders
- **Intelligent Task Generation**: Automatically categorizes tasks based on item characteristics
- **Priority Assignment**: Determines task priority based on delivery dates and order values
- **Multiple Output Formats**: Saves tasks in JSON, CSV, and summary text formats
- **Comprehensive Error Handling**: Robust error handling with detailed feedback

## Prerequisites

- Python 3.8+
- Required packages (install via `pip install -r requirements.txt`)
- ERPNext API credentials configured in `.env` file
- Access to ERPNext instance with Sales Order data

## Installation

1. Ensure you have the same `.env` file setup as used for `quotegen.py`:

```env
# --- ERPNext Staging ---
ERPNEXT_API_URL="https://staging.erp.yaadapps.com"
ERPNEXT_API_KEY="your_staging_api_key"
ERPNEXT_API_SECRET="your_staging_api_secret"

# --- ERPNext Production ---
ERPNEXT_PROD_API_URL="https://erp.yaadapps.com"
ERPNEXT_PROD_API_KEY="your_production_api_key"
ERPNEXT_PROD_API_SECRET="your_production_api_secret"
```

2. The script uses the existing `inputs/` and `outputs/` directory structure.

## Usage

### Basic Usage

```bash
python sales_order_tasks.py
```

This will:
1. Prompt you to select the target environment (Staging/Production)
2. Fetch submitted sales orders from the last 30 days
3. Display orders in a formatted table
4. Allow you to select an order
5. Generate tasks based on the order items
6. Save results in multiple formats

### Advanced Options

```bash
# Fetch orders from the last 60 days
python sales_order_tasks.py --days 60

# Process a specific sales order directly
python sales_order_tasks.py --order-id SAL-ORD-2025-00001
```

## Task Generation Logic

### Task Types

The script automatically categorizes tasks based on item characteristics:

- **Development**: Items containing keywords like 'develop', 'custom', 'software', 'app', 'system'
- **Design**: Items with 'design', 'ui', 'ux', 'graphic', 'logo'
- **Testing**: Items with 'test', 'qa', 'quality'
- **Deployment**: Items with 'deploy', 'install', 'setup', 'config'
- **Support**: Items with 'train', 'support', 'maintain'
- **Consulting**: Items with 'consult', 'analysis', 'planning'
- **General**: Default category for items that don't match specific patterns

### Priority Assignment

Task priority is determined by:
- **High**: Delivery within 7 days OR item value > $10,000
- **Medium**: Delivery within 14 days OR item value > $5,000
- **Low**: All other tasks

### Hour Estimation

Base hour estimates by task type:
- Development: 40 hours
- Design: 20 hours
- Testing: 16 hours
- Deployment: 8 hours
- Support: 4 hours
- Consulting: 8 hours
- General: 8 hours

Estimates are adjusted based on:
- Item quantity
- Item rate/value
- Complexity indicators

### Order-Level Tasks

The script automatically generates two order-level management tasks:
1. **Project Kickoff**: High-priority task for project initiation
2. **Final Delivery**: High-priority task for project completion

## Output Files

The script generates three types of output files in the `outputs/` directory:

1. **JSON File** (`{ORDER_ID}_tasks_{timestamp}.json`): Complete task data in JSON format
2. **CSV File** (`{ORDER_ID}_tasks_{timestamp}.csv`): Spreadsheet-compatible format
3. **Summary Report** (`{ORDER_ID}_task_summary_{timestamp}.txt`): Human-readable summary with statistics

## Output Structure

Each generated task includes:

```json
{
    "task_id": "TASK-SAL-ORD-2025-00001-001",
    "title": "Development: Custom CRM System",
    "description": "Complete Custom CRM System for ABC Company...",
    "task_type": "Development",
    "priority": "High",
    "estimated_hours": 60,
    "item_details": { /* Original ERPNext item data */ },
    "order_info": { /* Sales order metadata */ },
    "status": "Not Started",
    "assigned_to": "",
    "due_date": "2025-02-15",
    "created_date": "2025-01-22 14:30:00"
}
```

## Error Handling

The script includes comprehensive error handling for:
- Invalid API credentials
- Network connectivity issues
- Missing or invalid sales orders
- Data validation errors
- File system permissions

## Integration with Project Management Tools

The generated task files can be easily imported into various project management tools:
- **JSON**: For custom integrations and APIs
- **CSV**: For Excel, Google Sheets, or other spreadsheet tools
- **Summary**: For quick review and planning

## Examples

### Example 1: Regular Task Generation
```bash
$ python sales_order_tasks.py
--- Starting Yaad Apps Sales Order Task Generator ---
Select target environment:
[1] Staging
[2] Production
Enter choice (1 or 2): 1

🎯 TARGETING: STAGING ENVIRONMENT

🔍 Fetching sales orders from ERPNext...
✅ Successfully fetched 5 submitted sales orders.

====================================================================================================
AVAILABLE SALES ORDERS
====================================================================================================
#   Order ID        Customer                  Date        Total           Status              
----------------------------------------------------------------------------------------------------
1   <USER>     <GROUP> Company Ltd          2025-01-15  USD 25,000.00   To Deliver and Bill 
2   SAL-ORD-002     XYZ Corp                 2025-01-18  USD 15,500.00   To Deliver and Bill 
...

Select a sales order (1-5) or 'q' to quit: 1

✅ Selected: SAL-ORD-001 - ABC Company Ltd

🔧 Generating tasks for Sales Order: SAL-ORD-001
✅ Generated 8 tasks from sales order items.

================================================================================
TASK GENERATION SUMMARY
================================================================================
Total Tasks Generated: 8
Total Estimated Hours: 156
Estimated Duration: 19.5 working days

Task Types:
  Development: 3
  Design: 2
  Project Management: 2
  Testing: 1

Priorities:
  High: 4
  Medium: 3
  Low: 1
...

🚀 Task generation complete!
Generated 8 tasks for Sales Order: SAL-ORD-001
Files saved to: outputs/
```

### Example 2: Specific Order Processing
```bash
$ python sales_order_tasks.py --order-id SAL-ORD-2025-00001
--- Starting Yaad Apps Sales Order Task Generator ---
...
Fetching specific sales order: SAL-ORD-2025-00001
🔧 Generating tasks for Sales Order: SAL-ORD-2025-00001
...
```

## Troubleshooting

### Common Issues

1. **"No submitted sales orders found"**
   - Check the date range with `--days` parameter
   - Verify orders are submitted (docstatus = 1) in ERPNext
   - Ensure API credentials have proper permissions

2. **"Failed to get API credentials"**
   - Verify `.env` file exists and contains correct credentials
   - Check environment variable names match expected format

3. **"ValidationError" or API errors**
   - Verify ERPNext instance is accessible
   - Check API user permissions in ERPNext
   - Ensure sales order exists and is accessible

### Debug Mode

For detailed debugging, you can modify the script to add more verbose logging or use Python's debugging tools.

## Contributing

When contributing to this script, please maintain the same coding standards and patterns used in `quotegen.py`:
- Comprehensive error handling
- Clear user feedback with emojis and formatting
- Modular function design
- Consistent naming conventions
- Proper documentation

## License

This script is part of the Yaad Apps Scripts collection and follows the same licensing terms.
