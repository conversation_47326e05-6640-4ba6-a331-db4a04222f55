Project Scope Document: <PERSON><PERSON> Tuning Website
Document Version: 1.0
Date: July 14, 2025
Prepared for: <PERSON><PERSON> / <PERSON> Tuning
Prepared by: <PERSON> / <PERSON><PERSON>, Jamaica is the company address and the <NAME_EMAIL>

1. Project Overview
This document outlines the scope for the development of a high-energy, professional website for MaxHP Tuning. The primary goal of this project is to create a powerful digital presence that embodies the speed, performance, and top-tier reputation of the brand, exemplified by its record-breaking 8.1-second drag car.
The website will serve as a central hub for the brand, designed to:
Generate sales for performance parts and branded merchandise.
Showcase world-class tuning services and custom car builds.
Engage the community by promoting upcoming events.
Build brand authority and attract new customers and fans in the automotive and drag racing communities.
2. Core Functional Requirements (Phase 1)
The following features and functionalities are included in the initial project scope.
2.1. High-Impact Animated Landing Page
A visually stunning, dynamic landing page designed to capture the "8.1-second" energy of the brand.
Key Feature: A custom GSAP (GreenSock Animation Platform) animation featuring the client's signature purple drag car. This animation will create a cinematic, engaging introduction for visitors, immediately setting a high-performance tone.
2.2. Logo Reconstruction & Brand Kit
Re-creation of the modern "MaxHP Tuning" lion logo (based on supplied images) into a high-resolution, scalable vector format.
Delivery of a basic digital brand kit, including the finalized logo in various formats (for web, print, and merchandise) and a defined color palette.
2.3. E-commerce Store
A clean, easy-to-navigate online shop with two main categories:
Performance Parts: A catalog of auto parts for sale, with detailed descriptions, images, and pricing.
Merchandise: A section for selling branded apparel (T-shirts, hats) and accessories (decals, keychains).
Secure online payments will be integrated using a local Jamaican payment gateway (e.g., WiPay, Lynk).
A simple inventory tracking system for all products.
2.4. Services & Bookings
A dedicated section detailing the tuning and customization services offered by MaxHP Tuning.
A "Request a Quote" or "Book a Consultation" form for each service, which will capture customer details and project requirements, sending a notification directly to the business owner.
2.5. Events Calendar
A page to advertise and promote upcoming events, such as race meets, car shows, and customer appreciation days.
Each event listing will include the date, time, location, a descriptive overview, and a link to a map.
2.6. Project Gallery
A visually-driven portfolio to showcase MaxHP Tuning's work.
This will prominently feature high-quality photos and videos of the record-breaking drag car, as well as other impressive client builds.
2.7. Standard Website Pages
About Us: A page telling the story of the MaxHP Tuning brand and team.
Contact Us: A page with a contact form, email address, phone number, and an embedded map showing the business location.
3. Optional Features & Future Considerations (Phase 2)
The following features are not included in the initial project phase but can be developed in the future to further enhance the website.
Advanced E-commerce: Customer accounts, order history, product reviews, and waitlists for out-of-stock items.
Dyno Run Showcase: A dedicated section featuring videos and performance graphs from dynamometer tuning sessions.
Blog / Technical Articles: A content section to improve SEO and establish expertise with articles on tuning, car maintenance, and performance modifications.
Integrated Appointment Booking: A full calendar system allowing clients to self-book and pay for specific time slots for tuning services.
Community Forum: A private forum for MaxHP customers and fans to discuss builds, share tips, and interact with the brand.
4. Proposed Technology Stack
The application will be built using Yaad Apps' standard, robust technology stack to ensure reliability, security, and scalability.
Backend: Python with the Django framework
Database: PostgreSQL
Frontend: Django Templates, HTMX (for dynamic interactions), and GSAP (for feature animations).
Deployment: Hosted on a secure and reliable cloud provider (e.g., DigitalOcean).
5. Next Steps
Review & Approval: The client (Dwayne Porter) is asked to review this scope document to ensure it accurately reflects the project goals.
Formal Quote: Upon approval of this document, Yaad Apps will provide a formal project quote detailing the costs and an estimated timeline for completion.
Project Kickoff: Upon acceptance of the quote and payment of the initial deposit, Yaad Apps will begin the design and development process, starting with UI/UX mockups for key pages.

