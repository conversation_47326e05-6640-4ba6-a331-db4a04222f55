import os
import json
import sys
import re
import argparse
import time
import threading
import itertools
from pathlib import Path
from dotenv import load_dotenv
import google.generativeai as genai
import requests

# --- Configuration ---
INPUT_DIR = Path(__file__).parent / "inputs"
OUTPUT_DIR = Path(__file__).parent / "outputs"
SCOPE_FILE_DEFAULT = INPUT_DIR / "scope_document.txt"
ITEMS_BACKUP_FILE = INPUT_DIR / "items_backup.json"
EMAIL_TEMPLATE_FILE = INPUT_DIR / "email_template.html"
GEMINI_MODELS = ['gemini-2.5-pro', 'gemini-2.5-flash', 'gemini-1.5-flash'] 

# --- Helper Functions ---
def _normalize_customer_name(name):
    if not name: return ""
    name = name.lower()
    for suffix in ['ltd', 'limited', 'inc', 'co', 'llc']: name = name.replace(suffix, '')
    return re.sub(r'[\W_]+', '', name)

class Spinner:
    def __init__(self, message="Working..."):
        self._message = message
        self._spinner = itertools.cycle(['|', '/', '-', '\\'])
        self._running = False
        self._thread = None
    def _spin(self):
        while self._running:
            sys.stdout.write(f"\r{self._message} {next(self._spinner)}")
            sys.stdout.flush()
            time.sleep(0.1)
        sys.stdout.write(f"\r{' ' * (len(self._message) + 2)}\r")
        sys.stdout.flush()
    def start(self):
        self._running = True
        self._thread = threading.Thread(target=self._spin)
        self._thread.start()
    def stop(self):
        self._running = False
        if self._thread:
            self._thread.join()

# --- Main Functions ---
def get_api_credentials(env_prefix=""):
    load_dotenv()
    api_url = os.getenv(f"ERPNEXT{env_prefix}_API_URL")
    api_key = os.getenv(f"ERPNEXT{env_prefix}_API_KEY")
    api_secret = os.getenv(f"ERPNEXT{env_prefix}_API_SECRET")
    if not all([api_url, api_key, api_secret]):
        print(f"⚠️ WARNING: Could not find ERPNext credentials for prefix '{env_prefix}'. API calls will fail.")
        return None, None
    headers = {"Authorization": f"token {api_key}:{api_secret}", "Accept": "application/json", "Content-Type": "application/json", "Expect": ""}
    return headers, api_url

def get_or_load_erpnext_items(headers, api_url):
    if headers and api_url:
        print("Attempting to fetch live items from ERPNext...")
        items_endpoint = f"{api_url}/api/resource/Item"
        params = {"fields": '["item_code", "item_name", "item_group"]', "limit_page_length": 0}
        try:
            response = requests.get(items_endpoint, headers=headers, params=params)
            response.raise_for_status()
            items = response.json().get("data", [])
            with open(ITEMS_BACKUP_FILE, 'w') as f: json.dump(items, f, indent=4)
            print(f"✅ Successfully fetched {len(items)} items and updated local backup.")
            return items
        except requests.exceptions.RequestException as e:
            print(f"❌ Could not fetch live items from ERPNext: {e}")
    print("Falling back to local item backup...")
    if ITEMS_BACKUP_FILE.exists():
        with open(ITEMS_BACKUP_FILE, 'r') as f: backup_items = json.load(f)
        print(f"✅ Loaded {len(backup_items)} items from '{ITEMS_BACKUP_FILE}'.")
        return backup_items
    else:
        raise FileNotFoundError("API call failed and no local backup file was found. Cannot proceed.")

def build_prompt(scope_text, existing_items):
    items_str = json.dumps(existing_items, indent=2)
    return f"""
        You are an expert technical project manager for a Jamaican software company called "Yaad Apps". Your task is to analyze a project scope document and generate a single, complete, and valid JSON object for a customer quotation.
        Follow these rules strictly:
        1. From the "PROJECT SCOPE", you MUST extract the customer's company name and their full mailing address.
        2. From the "PROJECT SCOPE", extract the customer's primary email address if it is present. If not found, return null.
        3. Analyze the project requirements and break them down into a list of line items.
        4. For each line item, you MUST first try to match it to one of the "EXISTING ERPNEXT ITEMS". Prioritize reusing existing items.
        5. If no existing item is a good match, create a new item with a logical `item_code` (starting with 'SVC-') and `item_name`.
        6. For each line item: estimate the required hours (`qty`), set the `rate` (default 15.00), and calculate the `amount`.
        7. After creating all line items, calculate `grand_total` and `deposit_amount` (50% of grand total).
        8. Generate a friendly, professional `email_welcome_paragraph`.
        9. Your entire response MUST be only a single, valid JSON object. Do not include ```json or any text before or after the JSON.
        The required JSON output format is specified below. You must provide both raw numbers and formatted currency strings.
        {{
            "customer_name": "string", "customer_email": "string or null", "customer_address": "string", "company": "Yaad Apps",
            "grand_total": number, "grand_total_formatted": "string (e.g., $1,234.50 USD)",
            "deposit_amount": number, "deposit_amount_formatted": "string (e.g., $617.25 USD)",
            "email_welcome_paragraph": "string",
            "items": [
                {{"item_code": "string", "item_name": "string", "item_group": "Services", "qty": number, "rate": number, "amount": number, "description": "string"}}
            ]
        }}
        ---
        EXISTING ERPNEXT ITEMS:
        {items_str}
        ---
        PROJECT SCOPE:
        {scope_text}
        ---
    """

def generate_quote_data_from_ai(prompt):
    print("\nConnecting to Gemini API...")
    load_dotenv()
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key: raise ValueError("GEMINI_API_KEY not found in .env file.")
    genai.configure(api_key=api_key)
    for model_name in GEMINI_MODELS:
        spinner = Spinner(f"🤖 Sending prompt to Gemini using {model_name}...")
        try:
            spinner.start()
            model = genai.GenerativeModel(model_name)
            response = model.generate_content(prompt)
            spinner.stop()
            json_text = response.text.strip().replace("```json", "").replace("```", "")
            quote_data = json.loads(json_text)
            print(f"✅ Success with model '{model_name}'. Received and parsed quote data.")
            return quote_data
        except Exception as e:
            spinner.stop()
            print(f"⚠️ Model '{model_name}' failed: {e}. Trying next model...")
    print("❌ All Gemini models failed. Could not generate quote data.")
    sys.exit(1)

def find_or_create_customer(customer_name_from_ai, headers, api_url):
    if not customer_name_from_ai: raise ValueError("Customer name not found in quote data.")
    print(f"\nChecking for customer '{customer_name_from_ai}' in ERPNext...")
    customer_endpoint = f"{api_url}/api/resource/Customer"
    try:
        response = requests.get(customer_endpoint, headers=headers, params={"fields": '["name", "customer_name"]', "limit_page_length": 0})
        response.raise_for_status()
        normalized_target_name = _normalize_customer_name(customer_name_from_ai)
        for customer in response.json().get("data", []):
            normalized_existing_name = _normalize_customer_name(customer.get("customer_name"))
            if normalized_target_name == normalized_existing_name:
                customer_id = customer.get("name")
                print(f"✅ Found existing customer '{customer.get('customer_name')}' with ID: {customer_id}.")
                return customer_id, customer.get("customer_name")

        print(f"Customer '{customer_name_from_ai}' not found. Creating...")
        customer_payload = {"customer_name": customer_name_from_ai, "customer_type": "Company"}
        create_response = requests.post(customer_endpoint, headers=headers, json=customer_payload)
        create_response.raise_for_status()
        new_customer_id = create_response.json().get("data", {}).get("name")
        print(f"🚀 Successfully created customer '{customer_name_from_ai}' with ID: {new_customer_id}.")
        return new_customer_id, customer_name_from_ai
    except requests.exceptions.RequestException as e:
        print(f"❌ Error creating/checking customer '{customer_name_from_ai}': {e}")
        if e.response: print(f"Response Body: {e.response.text}")
        return None, None

def create_item_if_not_exists(item_details, headers, api_url):
    item_code = item_details.get("item_code")
    item_endpoint = f"{api_url}/api/resource/Item/{item_code}"

    try:
        response = requests.get(item_endpoint, headers=headers)
        if response.status_code == 200:
            existing = response.json().get("data")
            if existing.get("is_stock_item"):
                print(f"⚠️ Item '{item_code}' exists but is a stock item. Cloning and fixing...")

                fixed_code = f"{item_code}-SVC"
                check_fixed = requests.get(f"{api_url}/api/resource/Item/{fixed_code}", headers=headers)

                if check_fixed.status_code == 200:
                    print(f"⚠️ Clone '{fixed_code}' already exists. Skipping creation.")
                else:
                    new_item = item_details.copy()
                    new_item.update({
                        "item_code": fixed_code,
                        "item_name": f"{item_details.get('item_name')} (Service)",
                        "is_stock_item": 0,
                        "is_service_item": 1,
                        "disabled": 0,
                    })
                    create_response = requests.post(f"{api_url}/api/resource/Item", headers=headers, json=new_item)
                    create_response.raise_for_status()
                    print(f"🚀 Created service version of item: {fixed_code}")

                # Try disabling original — but don't fail if permission denied
                try:
                    disable_payload = {"disabled": 1}
                    disable_response = requests.put(item_endpoint, headers=headers, json=disable_payload)
                    disable_response.raise_for_status()
                    print(f"✅ Disabled original item: {item_code}")
                except requests.exceptions.RequestException as e:
                    print(f"⚠️ Could not disable original item '{item_code}': {e}")
                    if e.response:
                        print(f"Response Body: {e.response.text}")

                return True
            else:
                print(f"✅ Item '{item_code}' already exists and is a service. No action needed.")
                return True

        elif response.status_code == 404:
            print(f"Item '{item_code}' not found. Creating...")
            item_details.update({
                "is_stock_item": 0,
                "is_service_item": 1
            })
            create_response = requests.post(f"{api_url}/api/resource/Item", headers=headers, json=item_details)
            create_response.raise_for_status()
            print(f"🚀 Successfully created item '{item_code}'.")
            return True

        else:
            response.raise_for_status()

    except requests.exceptions.RequestException as e:
        print(f"❌ Error processing item '{item_code}': {e}")
        if e.response:
            print(f"Response Body: {e.response.text}")
        return False

def create_quotation(quote_data, customer_id, headers, api_url):
    print("\nCreating draft quotation in ERPNext...")
    quotation_endpoint = f"{api_url}/api/resource/Quotation"
    quotation_payload = {
        "customer": customer_id,
        "customer_name": quote_data.get("customer_name"),
        "company": "Yaad Apps", # FIXED: Use the exact company name found during testing
        "order_type": "Sales",
        "items": quote_data.get("items", [])
    }
    try:
        response = requests.post(quotation_endpoint, headers=headers, json=quotation_payload)
        response.raise_for_status()
        new_quote_name = response.json().get("data", {}).get("name")
        print(f"✅ Draft Quotation '{new_quote_name}' created successfully.")
        return new_quote_name
    except requests.exceptions.RequestException as e:
        print(f"❌ Error creating quotation: {e}")
        if e.response: print(f"Response Body: {e.response.text}")
        return None

def generate_email_html(quote_data, quote_name):
    print("Generating HTML email...")
    if not EMAIL_TEMPLATE_FILE.exists(): raise FileNotFoundError(f"Email template not found at {EMAIL_TEMPLATE_FILE}")
    template_html = EMAIL_TEMPLATE_FILE.read_text()
    populated_html = template_html.replace("{customer_name}", quote_data.get("customer_name", "")).replace("{email_welcome_paragraph}", quote_data.get("email_welcome_paragraph", "")).replace("{grand_total_formatted}", quote_data.get("grand_total_formatted", "")).replace("{deposit_amount_formatted}", quote_data.get("deposit_amount_formatted", ""))
    print("✅ Email generated.")
    return populated_html

def main():
    parser = argparse.ArgumentParser(description="Yaad Apps Quote Generation Tool.")
    group = parser.add_mutually_exclusive_group(required=False)
    group.add_argument("--scope", type=str, default=str(SCOPE_FILE_DEFAULT), help="Path to the scope document text file.")
    group.add_argument("--from-file", type=str, help="Path to a previously saved quote_data.json file to re-generate from.")
    args = parser.parse_args()
    print("--- Starting Yaad Apps QuoteGen Tool ---")
    env_prefix, env_name = "", ""
    while True:
        choice = input("Select target environment:\n[1] Staging\n[2] Production\nEnter choice (1 or 2): ")
        if choice == '1': env_prefix, env_name = "", "STAGING"; break
        elif choice == '2':
            env_prefix, env_name = "_PROD", "PRODUCTION"
            confirm = input(f"\n⚠️ You are targeting the PRODUCTION environment. Are you sure? (y/n): ").lower()
            if confirm == 'y': break
            else: print("Operation cancelled."); sys.exit()
        else: print("Invalid choice. Please enter 1 or 2.")
    print(f"\n🎯 TARGETING: {env_name} ENVIRONMENT\n")

    try:
        erp_headers, erp_api_url = get_api_credentials(env_prefix)
        quote_data = None
        
        if args.from_file:
            print(f"Loading quote data from file: {args.from_file}")
            with open(args.from_file, 'r') as f: quote_data = json.load(f)
        else:
            scope_file_path = Path(args.scope)
            if not scope_file_path.exists(): raise FileNotFoundError(f"Scope file not found: {scope_file_path}")
            
            items = get_or_load_erpnext_items(erp_headers, erp_api_url)
            scope_text = scope_file_path.read_text()
            prompt = build_prompt(scope_text, items)
            quote_data = generate_quote_data_from_ai(prompt)

            if quote_data:
                OUTPUT_DIR.mkdir(exist_ok=True)
                temp_save_path = OUTPUT_DIR / f"temp_{_normalize_customer_name(quote_data.get('customer_name'))}_data.json"
                with open(temp_save_path, 'w') as f: json.dump(quote_data, f, indent=4)
                print(f"📝 Gemini response saved to '{temp_save_path}'")

        if not quote_data: raise Exception("Failed to get quote data.")

        customer_id, exact_customer_name = find_or_create_customer(quote_data.get("customer_name"), erp_headers, erp_api_url)
        if not customer_id:
            raise Exception("Failed to create/verify customer in ERPNext. Aborting.")
        
        quote_data['customer_name'] = exact_customer_name

        if not all(create_item_if_not_exists(item, erp_headers, erp_api_url) for item in quote_data["items"]):
             raise Exception("Could not create/verify all items in ERPNext. Aborting.")
        
        new_quote_name = create_quotation(quote_data, customer_id, erp_headers, erp_api_url)
        if not new_quote_name: raise Exception("Failed to create quotation in ERPNext. Aborting.")
            
        email_content = generate_email_html(quote_data, new_quote_name)
        
        OUTPUT_DIR.mkdir(exist_ok=True)
        final_quote_data_file = OUTPUT_DIR / f"{new_quote_name}_data.json"
        if 'temp_save_path' in locals() and temp_save_path.exists():
            temp_save_path.rename(final_quote_data_file)
        
        email_file = OUTPUT_DIR / f"{new_quote_name}_email.html"
        with open(email_file, 'w') as f: f.write(email_content)
            
        print("\n--- 🚀 Process Complete! ---")
        print(f"Quote data saved to: {final_quote_data_file}")
        print(f"Email draft saved to: {email_file}")
        print("----------------------------")

    except Exception as e:
        print(f"\n❌ A critical error occurred: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()