<div align="center">
<img src="https://yaadapps-assets.atl1.cdn.digitaloceanspaces.com/logo/logo_full.png" alt="Yaad Apps Logo" width="300"/>
</div>

# Yaad Apps: Automation & Admin Scripts

<p align="center">
<img src="https://img.shields.io/badge/Project-Admin Scripts-ed9704" alt="Project Badge">
<img src="https://img.shields.io/badge/Status-Active-018144" alt="Status Badge">
<img src="https://img.shields.io/badge/Language-Python-85e2af" alt="Language Badge">
</p>

---

## 1. Introduction

This repository contains a collection of Python scripts designed to automate administrative and operational tasks for Yaad Apps. The primary tool is the QuoteGen script, which leverages the Gemini API to analyze project scope documents and fully automate the process of creating draft quotations in ERPNext.

These scripts are intended to increase efficiency, reduce manual data entry, and ensure consistency in the sales and quoting process.

---

## 2. Prerequisites

Before using these scripts, ensure you have the following installed and configured:

- Python 3.8+
- `pip` for package management
- API keys for ERPNext (Staging & Production) and the Gemini API

---

## 3. Setup Instructions

### 1. Clone the Repository

~~~bash
# Clone your repository from GitHub
git clone <your-repo-url>
cd yaadapps-scripts
~~~

### 2. Create & Activate Virtual Environment

It is crucial to use a virtual environment to manage dependencies.

~~~bash
# Create the virtual environment
python -m venv .venv

# Activate it (for PowerShell)
. \.venv\Scripts\activate
~~~

You should see `(.venv)` at the beginning of your terminal prompt.

### 3. Install Dependencies

Install all the required Python libraries from the `requirements.txt` file.

~~~bash
pip install -r requirements.txt
~~~

### 4. Configure Environment Variables

Create a `.env` file in the root of the project to securely store your API keys. Copy the contents of `.env.example` or use the template below.

~~~env
# .env file

# --- ERPNext Staging ---
ERPNEXT_API_URL="https://staging.erp.yaadapps.com"
ERPNEXT_API_KEY="your_staging_api_key"
ERPNEXT_API_SECRET="your_staging_api_secret"

# --- ERPNext Production ---
ERPNEXT_PROD_API_URL="https://erp.yaadapps.com"
ERPNEXT_PROD_API_KEY="your_production_api_key"
ERPNEXT_PROD_API_SECRET="your_production_api_secret"

# --- Gemini API ---
GEMINI_API_KEY="your_gemini_api_key"
~~~

> **Important**: The `.gitignore` file should be configured to prevent your `.env` file from being committed to the repository.

---

## 4. Usage

The main tool in this repository is `quotegen.py`. It has two primary modes of operation controlled by command-line arguments.

### A. Generating a New Quote from a Scope Document

This is the default mode. The script reads a scope document, fetches the latest items from ERPNext, uses the Gemini API to generate quote data, and creates the quote in your selected ERPNext environment.

#### 1. Prepare your input files:

- Place the project scope text in `inputs/scope_document.txt`.
- The script will automatically fetch ERPNext items, but will use `inputs/items_backup.json` as a fallback if the API call fails.

#### 2. Run the script:

~~~bash
python quotegen.py
~~~

Or to be explicit:

~~~bash
python quotegen.py --scope inputs/scope_document.txt
~~~

The script will then prompt you to select the target environment (Staging or Production).

### B. Re-running a Quote from a Saved File

This mode is useful for debugging or re-generating an email without making another call to the Gemini API. It uses a previously generated `_data.json` file from the `outputs/` folder.

#### 1. Run the script with the `--from-file` argument:

~~~bash
python quotegen.py --from-file outputs/SAL-QTN-2025-0000X_data.json
~~~

This will skip the Gemini API call and proceed directly to creating the customer, items, and quote in your selected ERPNext environment using the data from the specified file.

---

## 5. Scripts Overview

| Script           | Description                                                                 |
|------------------|-----------------------------------------------------------------------------|
| `quotegen.py`    | The primary, all-in-one tool for automating quote creation from a scope document using AI and ERPNext APIs. |
| `get_items.py`   | A simple utility to fetch and display all current items from an ERPNext instance. |
| `get_customers.py` | A simple utility to fetch and display all current customers from an ERPNext instance. |
| `get_terms.py`   | A simple utility to fetch and display all "Terms and Conditions" templates from an ERPNext instance. |
| `create_quote.py`| The original, simpler script for creating quotes from a static data block. Now largely superseded by `quotegen.py`. |
