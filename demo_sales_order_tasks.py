#!/usr/bin/env python3
"""
Demo script to showcase the Sales Order Task Generator functionality
This script creates a mock sales order and demonstrates task generation
without requiring actual ERPNext API access.
"""

import json
from datetime import datetime, timedelta
from sales_order_tasks import (
    generate_tasks_from_order, 
    display_task_summary, 
    save_tasks_to_files,
    determine_task_type,
    determine_priority,
    estimate_hours
)

def create_mock_sales_order():
    """Create a mock sales order for demonstration purposes."""
    return {
        "name": "SAL-ORD-DEMO-001",
        "customer": "DEMO-CUSTOMER-001",
        "customer_name": "Demo Tech Solutions Ltd",
        "transaction_date": "2025-01-20",
        "delivery_date": "2025-02-15",
        "status": "To Deliver and Bill",
        "grand_total": 45000.00,
        "currency": "USD",
        "company": "Yaad Apps",
        "territory": "Jamaica",
        "items": [
            {
                "item_code": "SVC-CUSTOM-CRM",
                "item_name": "Custom CRM System Development",
                "description": "Develop a comprehensive customer relationship management system with user authentication, contact management, and reporting features",
                "qty": 1,
                "rate": 25000.00,
                "amount": 25000.00,
                "uom": "Project",
                "stock_uom": "Project"
            },
            {
                "item_code": "SVC-UI-DESIGN",
                "item_name": "User Interface Design Package",
                "description": "Complete UI/UX design for the CRM system including wireframes, mockups, and interactive prototypes",
                "qty": 1,
                "rate": 8000.00,
                "amount": 8000.00,
                "uom": "Package",
                "stock_uom": "Package"
            },
            {
                "item_code": "SVC-MOBILE-APP",
                "item_name": "Mobile App Development",
                "description": "Native mobile application for iOS and Android with offline sync capabilities",
                "qty": 2,
                "rate": 4000.00,
                "amount": 8000.00,
                "uom": "Platform",
                "stock_uom": "Platform"
            },
            {
                "item_code": "SVC-TESTING-QA",
                "item_name": "Quality Assurance Testing",
                "description": "Comprehensive testing including unit tests, integration tests, and user acceptance testing",
                "qty": 1,
                "rate": 2000.00,
                "amount": 2000.00,
                "uom": "Package",
                "stock_uom": "Package"
            },
            {
                "item_code": "SVC-DEPLOYMENT",
                "item_name": "System Deployment and Setup",
                "description": "Deploy system to production environment, configure servers, and setup monitoring",
                "qty": 1,
                "rate": 1500.00,
                "amount": 1500.00,
                "uom": "Service",
                "stock_uom": "Service"
            },
            {
                "item_code": "SVC-TRAINING",
                "item_name": "User Training and Support",
                "description": "Provide comprehensive user training and 3 months of post-launch support",
                "qty": 1,
                "rate": 500.00,
                "amount": 500.00,
                "uom": "Package",
                "stock_uom": "Package"
            }
        ]
    }

def demo_task_categorization():
    """Demonstrate the task categorization logic."""
    print("\n" + "="*80)
    print("TASK CATEGORIZATION DEMO")
    print("="*80)
    
    sample_items = [
        ("SVC-CUSTOM-CRM", "Custom CRM System Development", "Develop a comprehensive CRM system"),
        ("SVC-UI-DESIGN", "User Interface Design", "Create wireframes and mockups"),
        ("SVC-MOBILE-APP", "Mobile App Development", "Native mobile application"),
        ("SVC-TESTING-QA", "Quality Assurance Testing", "Unit tests and integration testing"),
        ("SVC-DEPLOYMENT", "System Deployment", "Deploy to production environment"),
        ("SVC-TRAINING", "User Training", "Provide user training and support"),
        ("SVC-CONSULTING", "Technical Consulting", "Analysis and planning consultation"),
        ("SVC-GENERAL", "General Service", "Miscellaneous service item")
    ]
    
    print(f"{'Item Code':<20} {'Task Type':<15} {'Description'}")
    print("-" * 80)
    
    for item_code, item_name, description in sample_items:
        task_type = determine_task_type(item_code, item_name, description)
        print(f"{item_code:<20} {task_type:<15} {description[:40]}...")

def demo_priority_assignment():
    """Demonstrate priority assignment logic."""
    print("\n" + "="*80)
    print("PRIORITY ASSIGNMENT DEMO")
    print("="*80)
    
    # Create test scenarios
    scenarios = [
        {
            "name": "High Priority - Urgent Delivery",
            "order": {"delivery_date": (datetime.now() + timedelta(days=5)).strftime("%Y-%m-%d")},
            "item": {"amount": 5000}
        },
        {
            "name": "High Priority - High Value",
            "order": {"delivery_date": (datetime.now() + timedelta(days=20)).strftime("%Y-%m-%d")},
            "item": {"amount": 15000}
        },
        {
            "name": "Medium Priority - Normal",
            "order": {"delivery_date": (datetime.now() + timedelta(days=10)).strftime("%Y-%m-%d")},
            "item": {"amount": 7000}
        },
        {
            "name": "Low Priority - Standard",
            "order": {"delivery_date": (datetime.now() + timedelta(days=25)).strftime("%Y-%m-%d")},
            "item": {"amount": 2000}
        }
    ]
    
    print(f"{'Scenario':<30} {'Priority':<10} {'Delivery Date':<15} {'Amount'}")
    print("-" * 80)
    
    for scenario in scenarios:
        priority = determine_priority(scenario["item"], scenario["order"])
        delivery = scenario["order"]["delivery_date"]
        amount = f"${scenario['item']['amount']:,}"
        print(f"{scenario['name']:<30} {priority:<10} {delivery:<15} {amount}")

def demo_hour_estimation():
    """Demonstrate hour estimation logic."""
    print("\n" + "="*80)
    print("HOUR ESTIMATION DEMO")
    print("="*80)
    
    task_types = ["Development", "Design", "Testing", "Deployment", "Support", "Consulting", "General"]
    
    print(f"{'Task Type':<15} {'Base Hours':<12} {'With Qty=2':<12} {'High Value':<12}")
    print("-" * 60)
    
    for task_type in task_types:
        base_item = {"qty": 1, "rate": 100}
        qty_item = {"qty": 2, "rate": 100}
        high_value_item = {"qty": 1, "rate": 1500}
        
        base_hours = estimate_hours(base_item, task_type)
        qty_hours = estimate_hours(qty_item, task_type)
        high_value_hours = estimate_hours(high_value_item, task_type)
        
        print(f"{task_type:<15} {base_hours:<12} {qty_hours:<12} {high_value_hours:<12}")

def main():
    """Run the complete demonstration."""
    print("🚀 SALES ORDER TASK GENERATOR DEMONSTRATION")
    print("=" * 80)
    print("This demo showcases the functionality without requiring ERPNext API access.")
    print("=" * 80)
    
    # Show task categorization logic
    demo_task_categorization()
    
    # Show priority assignment logic
    demo_priority_assignment()
    
    # Show hour estimation logic
    demo_hour_estimation()
    
    # Generate tasks from mock order
    print("\n" + "="*80)
    print("FULL TASK GENERATION DEMO")
    print("="*80)
    
    mock_order = create_mock_sales_order()
    
    print(f"Mock Sales Order: {mock_order['name']}")
    print(f"Customer: {mock_order['customer_name']}")
    print(f"Total: {mock_order['currency']} {mock_order['grand_total']:,}")
    print(f"Items: {len(mock_order['items'])}")
    print(f"Delivery Date: {mock_order['delivery_date']}")
    
    # Generate tasks
    tasks = generate_tasks_from_order(mock_order)
    
    if tasks:
        # Display summary
        display_task_summary(tasks)
        
        # Save to files
        print(f"\n📁 Saving demo tasks to outputs directory...")
        save_tasks_to_files(tasks, mock_order['name'])
        
        print(f"\n✅ Demo completed successfully!")
        print(f"Generated {len(tasks)} tasks for demonstration.")
        print(f"Check the outputs/ directory for generated files.")
    else:
        print("❌ No tasks were generated.")

if __name__ == "__main__":
    main()
