import os
import requests
from dotenv import load_dotenv

def fetch_erpnext_terms():
    """
    Connects to the ERPNext API and fetches all Terms and Conditions templates.
    """
    load_dotenv()
    api_url = os.getenv("ERPNEXT_API_URL")
    api_key = os.getenv("ERPNEXT_API_KEY")
    api_secret = os.getenv("ERPNEXT_API_SECRET")

    if not all([api_url, api_key, api_secret]):
        print("Error: Missing required environment variables.")
        return

    # Note: The DocType name has spaces. The 'requests' library handles URL encoding.
    terms_endpoint = f"{api_url}/api/resource/Terms and Conditions"
    headers = {
        "Authorization": f"token {api_key}:{api_secret}",
        "Accept": "application/json",
    }
    params = {
        # Fetch the 'name' (unique ID) and the 'title' for display
        "fields": '["name", "title"]',
        "limit_page_length": 0
    }

    try:
        print(f"Attempting to fetch Terms and Conditions from {api_url}...")
        response = requests.get(terms_endpoint, headers=headers, params=params)
        response.raise_for_status()
        
        terms_list = response.json().get("data", [])

        if not terms_list:
            print("Successfully connected, but no Terms and Conditions templates were found.")
        else:
            print("\n--- ✅ Success! Found the following Terms and Conditions: ---")
            for terms in terms_list:
                # 'name' is the unique ID you will use in other API calls.
                print(f"- Name: {terms['name']} (Title: {terms.get('title', 'N/A')})")
            print("-----------------------------------------------------------\n")
            print("Note: Use the exact 'Name' from this list in your quote script.")

    except requests.exceptions.RequestException as e:
        print(f"❌ An error occurred: {e}")
        if e.response:
            print(f"Response Body: {e.response.text}")

if __name__ == "__main__":
    fetch_erpnext_terms()