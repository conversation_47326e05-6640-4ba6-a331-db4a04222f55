import os
import json
import sys
import re
import argparse
import time
import threading
import itertools
from pathlib import Path
from dotenv import load_dotenv
import requests
from datetime import datetime, timedelta

# --- Configuration ---
OUTPUT_DIR = Path(__file__).parent / "outputs"
TASKS_BACKUP_FILE = Path(__file__).parent / "inputs" / "tasks_backup.json"

# --- Helper Functions ---
def _normalize_customer_name(name):
    if not name: return ""
    name = name.lower()
    for suffix in ['ltd', 'limited', 'inc', 'co', 'llc']: name = name.replace(suffix, '')
    return re.sub(r'[\W_]+', '', name)

class Spinner:
    def __init__(self, message="Working..."):
        self._message = message
        self._spinner = itertools.cycle(['|', '/', '-', '\\'])
        self._running = False
        self._thread = None
    def _spin(self):
        while self._running:
            sys.stdout.write(f"\r{self._message} {next(self._spinner)}")
            sys.stdout.flush()
            time.sleep(0.1)
        sys.stdout.write(f"\r{' ' * (len(self._message) + 2)}\r")
        sys.stdout.flush()
    def start(self):
        self._running = True
        self._thread = threading.Thread(target=self._spin)
        self._thread.start()
    def stop(self):
        self._running = False
        if self._thread:
            self._thread.join()

# --- Main Functions ---
def get_api_credentials(env_prefix=""):
    load_dotenv()
    api_url = os.getenv(f"ERPNEXT{env_prefix}_API_URL")
    api_key = os.getenv(f"ERPNEXT{env_prefix}_API_KEY")
    api_secret = os.getenv(f"ERPNEXT{env_prefix}_API_SECRET")
    if not all([api_url, api_key, api_secret]):
        print(f"⚠️ WARNING: Could not find ERPNext credentials for prefix '{env_prefix}'. API calls will fail.")
        return None, None
    headers = {"Authorization": f"token {api_key}:{api_secret}", "Accept": "application/json", "Content-Type": "application/json", "Expect": ""}
    return headers, api_url

def fetch_submitted_sales_orders(headers, api_url, days_back=30):
    """Fetch submitted sales orders from ERPNext within the specified time range."""
    if not headers or not api_url:
        print("❌ No valid API credentials provided.")
        return []
    
    print(f"Fetching submitted sales orders from the last {days_back} days...")
    
    # Calculate date range
    end_date = datetime.now().strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=days_back)).strftime("%Y-%m-%d")
    
    sales_order_endpoint = f"{api_url}/api/resource/Sales Order"
    
    # Define fields to fetch
    fields = [
        "name", "customer", "customer_name", "transaction_date", 
        "delivery_date", "status", "grand_total", "currency",
        "items", "company", "territory"
    ]
    
    params = {
        "fields": json.dumps(fields),
        "filters": json.dumps([
            ["status", "=", "To Deliver and Bill"],
            ["docstatus", "=", 1],  # Only submitted orders
            ["transaction_date", ">=", start_date],
            ["transaction_date", "<=", end_date]
        ]),
        "limit_page_length": 0
    }
    
    try:
        spinner = Spinner("🔍 Fetching sales orders from ERPNext...")
        spinner.start()
        
        response = requests.get(sales_order_endpoint, headers=headers, params=params)
        response.raise_for_status()
        
        spinner.stop()
        
        orders = response.json().get("data", [])
        print(f"✅ Successfully fetched {len(orders)} submitted sales orders.")
        
        # Fetch detailed items for each order
        for order in orders:
            order_name = order.get("name")
            items_response = requests.get(f"{api_url}/api/resource/Sales Order/{order_name}", headers=headers)
            if items_response.status_code == 200:
                detailed_order = items_response.json().get("data", {})
                order["items"] = detailed_order.get("items", [])
        
        return orders
        
    except requests.exceptions.RequestException as e:
        spinner.stop()
        print(f"❌ Error fetching sales orders: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response Body: {e.response.text}")
        return []

def display_sales_orders(orders):
    """Display sales orders in a formatted table for user selection."""
    if not orders:
        print("No sales orders found.")
        return
    
    print("\n" + "="*100)
    print("AVAILABLE SALES ORDERS")
    print("="*100)
    print(f"{'#':<3} {'Order ID':<15} {'Customer':<25} {'Date':<12} {'Total':<15} {'Status':<20}")
    print("-"*100)
    
    for idx, order in enumerate(orders, 1):
        order_id = order.get("name", "N/A")
        customer = order.get("customer_name", "N/A")[:24]
        date = order.get("transaction_date", "N/A")
        total = f"{order.get('currency', 'USD')} {order.get('grand_total', 0):,.2f}"
        status = order.get("status", "N/A")[:19]
        
        print(f"{idx:<3} {order_id:<15} {customer:<25} {date:<12} {total:<15} {status:<20}")
    
    print("-"*100)

def select_sales_order(orders):
    """Allow user to select a sales order from the list."""
    if not orders:
        return None
    
    while True:
        try:
            choice = input(f"\nSelect a sales order (1-{len(orders)}) or 'q' to quit: ").strip()
            
            if choice.lower() == 'q':
                print("Operation cancelled.")
                return None
            
            choice_idx = int(choice) - 1
            if 0 <= choice_idx < len(orders):
                selected_order = orders[choice_idx]
                print(f"\n✅ Selected: {selected_order.get('name')} - {selected_order.get('customer_name')}")
                return selected_order
            else:
                print(f"Invalid choice. Please enter a number between 1 and {len(orders)}.")
                
        except ValueError:
            print("Invalid input. Please enter a number or 'q' to quit.")

def generate_tasks_from_order(order):
    """Generate tasks based on sales order items and requirements."""
    if not order or not order.get("items"):
        print("❌ No items found in the selected sales order.")
        return []
    
    print(f"\n🔧 Generating tasks for Sales Order: {order.get('name')}")
    
    tasks = []
    order_info = {
        "order_id": order.get("name"),
        "customer": order.get("customer_name"),
        "order_date": order.get("transaction_date"),
        "delivery_date": order.get("delivery_date"),
        "total_amount": order.get("grand_total"),
        "currency": order.get("currency", "USD")
    }
    
    # Generate tasks based on items
    for idx, item in enumerate(order.get("items", []), 1):
        item_code = item.get("item_code", "")
        item_name = item.get("item_name", "")
        qty = item.get("qty", 1)
        description = item.get("description", "")
        
        # Determine task type based on item characteristics
        task_type = determine_task_type(item_code, item_name, description)
        priority = determine_priority(item, order)
        estimated_hours = estimate_hours(item, task_type)
        
        task = {
            "task_id": f"TASK-{order.get('name')}-{idx:03d}",
            "title": f"{task_type}: {item_name}",
            "description": f"Complete {item_name} for {order.get('customer_name')}\n"
                          f"Quantity: {qty}\n"
                          f"Item Code: {item_code}\n"
                          f"Details: {description}",
            "task_type": task_type,
            "priority": priority,
            "estimated_hours": estimated_hours,
            "item_details": item,
            "order_info": order_info,
            "status": "Not Started",
            "assigned_to": "",
            "due_date": order.get("delivery_date"),
            "created_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        tasks.append(task)
    
    # Add order-level tasks
    order_tasks = generate_order_level_tasks(order, order_info)
    tasks.extend(order_tasks)
    
    print(f"✅ Generated {len(tasks)} tasks from sales order items.")
    return tasks

def determine_task_type(item_code, item_name, description):
    """Determine task type based on item characteristics."""
    item_text = f"{item_code} {item_name} {description}".lower()
    
    if any(keyword in item_text for keyword in ['develop', 'custom', 'software', 'app', 'system']):
        return "Development"
    elif any(keyword in item_text for keyword in ['design', 'ui', 'ux', 'graphic', 'logo']):
        return "Design"
    elif any(keyword in item_text for keyword in ['test', 'qa', 'quality']):
        return "Testing"
    elif any(keyword in item_text for keyword in ['deploy', 'install', 'setup', 'config']):
        return "Deployment"
    elif any(keyword in item_text for keyword in ['train', 'support', 'maintain']):
        return "Support"
    elif any(keyword in item_text for keyword in ['consult', 'analysis', 'planning']):
        return "Consulting"
    else:
        return "General"

def determine_priority(item, order):
    """Determine task priority based on various factors."""
    delivery_date = order.get("delivery_date")
    if delivery_date:
        try:
            delivery = datetime.strptime(delivery_date, "%Y-%m-%d")
            days_until_delivery = (delivery - datetime.now()).days
            
            if days_until_delivery <= 7:
                return "High"
            elif days_until_delivery <= 14:
                return "Medium"
            else:
                return "Low"
        except:
            pass
    
    # Check item amount for priority
    amount = item.get("amount", 0)
    if amount > 10000:
        return "High"
    elif amount > 5000:
        return "Medium"
    else:
        return "Low"

def estimate_hours(item, task_type):
    """Estimate hours required based on item and task type."""
    base_hours = {
        "Development": 40,
        "Design": 20,
        "Testing": 16,
        "Deployment": 8,
        "Support": 4,
        "Consulting": 8,
        "General": 8
    }
    
    qty = item.get("qty", 1)
    rate = item.get("rate", 0)
    
    # Adjust based on quantity and rate
    estimated = base_hours.get(task_type, 8) * qty
    
    # Adjust based on item value
    if rate > 1000:
        estimated *= 1.5
    elif rate > 500:
        estimated *= 1.2
    
    return max(1, int(estimated))

def generate_order_level_tasks(order, order_info):
    """Generate order-level management tasks."""
    order_tasks = []
    
    # Project kickoff task
    kickoff_task = {
        "task_id": f"TASK-{order.get('name')}-KICKOFF",
        "title": f"Project Kickoff - {order.get('customer_name')}",
        "description": f"Initiate project for Sales Order {order.get('name')}\n"
                      f"- Review requirements\n"
                      f"- Set up project structure\n"
                      f"- Schedule team meetings\n"
                      f"- Confirm delivery timeline",
        "task_type": "Project Management",
        "priority": "High",
        "estimated_hours": 4,
        "item_details": {},
        "order_info": order_info,
        "status": "Not Started",
        "assigned_to": "",
        "due_date": order.get("transaction_date"),
        "created_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    # Final delivery task
    delivery_task = {
        "task_id": f"TASK-{order.get('name')}-DELIVERY",
        "title": f"Final Delivery - {order.get('customer_name')}",
        "description": f"Complete final delivery for Sales Order {order.get('name')}\n"
                      f"- Final testing and QA\n"
                      f"- Package deliverables\n"
                      f"- Customer handover\n"
                      f"- Documentation delivery",
        "task_type": "Project Management",
        "priority": "High",
        "estimated_hours": 8,
        "item_details": {},
        "order_info": order_info,
        "status": "Not Started",
        "assigned_to": "",
        "due_date": order.get("delivery_date"),
        "created_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    order_tasks.extend([kickoff_task, delivery_task])
    return order_tasks

def save_tasks_to_files(tasks, order_name):
    """Save generated tasks to multiple output formats."""
    if not tasks:
        print("❌ No tasks to save.")
        return

    OUTPUT_DIR.mkdir(exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Save as JSON
    json_file = OUTPUT_DIR / f"{order_name}_tasks_{timestamp}.json"
    with open(json_file, 'w') as f:
        json.dump(tasks, f, indent=4, default=str)
    print(f"📄 Tasks saved to JSON: {json_file}")

    # Save as CSV
    csv_file = OUTPUT_DIR / f"{order_name}_tasks_{timestamp}.csv"
    import csv
    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        if tasks:
            writer = csv.DictWriter(f, fieldnames=tasks[0].keys())
            writer.writeheader()
            for task in tasks:
                # Flatten complex fields for CSV
                row = task.copy()
                row['item_details'] = json.dumps(task.get('item_details', {}))
                row['order_info'] = json.dumps(task.get('order_info', {}))
                writer.writerow(row)
    print(f"📊 Tasks saved to CSV: {csv_file}")

    # Save summary report
    summary_file = OUTPUT_DIR / f"{order_name}_task_summary_{timestamp}.txt"
    with open(summary_file, 'w') as f:
        f.write(f"TASK GENERATION SUMMARY\n")
        f.write(f"="*50 + "\n\n")
        f.write(f"Sales Order: {order_name}\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total Tasks: {len(tasks)}\n\n")

        # Task type breakdown
        task_types = {}
        priorities = {}
        total_hours = 0

        for task in tasks:
            task_type = task.get('task_type', 'Unknown')
            priority = task.get('priority', 'Unknown')
            hours = task.get('estimated_hours', 0)

            task_types[task_type] = task_types.get(task_type, 0) + 1
            priorities[priority] = priorities.get(priority, 0) + 1
            total_hours += hours

        f.write("TASK BREAKDOWN BY TYPE:\n")
        f.write("-" * 25 + "\n")
        for task_type, count in sorted(task_types.items()):
            f.write(f"{task_type}: {count} tasks\n")

        f.write("\nTASK BREAKDOWN BY PRIORITY:\n")
        f.write("-" * 30 + "\n")
        for priority, count in sorted(priorities.items()):
            f.write(f"{priority}: {count} tasks\n")

        f.write(f"\nTOTAL ESTIMATED HOURS: {total_hours}\n")
        f.write(f"ESTIMATED DAYS (8h/day): {total_hours/8:.1f}\n\n")

        f.write("DETAILED TASK LIST:\n")
        f.write("-" * 20 + "\n")
        for task in tasks:
            f.write(f"\n{task.get('task_id')}: {task.get('title')}\n")
            f.write(f"  Type: {task.get('task_type')} | Priority: {task.get('priority')} | Hours: {task.get('estimated_hours')}\n")
            f.write(f"  Due: {task.get('due_date')} | Status: {task.get('status')}\n")
            f.write(f"  Description: {task.get('description', '')[:100]}...\n")

    print(f"📋 Task summary saved: {summary_file}")

def display_task_summary(tasks):
    """Display a summary of generated tasks."""
    if not tasks:
        print("No tasks to display.")
        return

    print(f"\n{'='*80}")
    print(f"TASK GENERATION SUMMARY")
    print(f"{'='*80}")

    # Count by type and priority
    task_types = {}
    priorities = {}
    total_hours = 0

    for task in tasks:
        task_type = task.get('task_type', 'Unknown')
        priority = task.get('priority', 'Unknown')
        hours = task.get('estimated_hours', 0)

        task_types[task_type] = task_types.get(task_type, 0) + 1
        priorities[priority] = priorities.get(priority, 0) + 1
        total_hours += hours

    print(f"Total Tasks Generated: {len(tasks)}")
    print(f"Total Estimated Hours: {total_hours}")
    print(f"Estimated Duration: {total_hours/8:.1f} working days")

    print(f"\nTask Types:")
    for task_type, count in sorted(task_types.items()):
        print(f"  {task_type}: {count}")

    print(f"\nPriorities:")
    for priority, count in sorted(priorities.items()):
        print(f"  {priority}: {count}")

    print(f"\n{'='*80}")

    # Show first few tasks as preview
    print(f"TASK PREVIEW (First 5 tasks):")
    print(f"{'-'*80}")
    for i, task in enumerate(tasks[:5]):
        print(f"{i+1}. {task.get('title')}")
        print(f"   Type: {task.get('task_type')} | Priority: {task.get('priority')} | Hours: {task.get('estimated_hours')}")
        print(f"   Due: {task.get('due_date')}")
        print()

    if len(tasks) > 5:
        print(f"... and {len(tasks) - 5} more tasks")
    print(f"{'-'*80}")

def main():
    parser = argparse.ArgumentParser(description="Yaad Apps Sales Order Task Generator")
    parser.add_argument("--days", type=int, default=30, help="Number of days back to fetch sales orders (default: 30)")
    parser.add_argument("--order-id", type=str, help="Specific sales order ID to process (skips selection)")
    args = parser.parse_args()

    print("--- Starting Yaad Apps Sales Order Task Generator ---")

    # Environment selection
    env_prefix, env_name = "", ""
    while True:
        choice = input("Select target environment:\n[1] Staging\n[2] Production\nEnter choice (1 or 2): ")
        if choice == '1':
            env_prefix, env_name = "", "STAGING"
            break
        elif choice == '2':
            env_prefix, env_name = "_PROD", "PRODUCTION"
            confirm = input(f"\n⚠️ You are targeting the PRODUCTION environment. Are you sure? (y/n): ").lower()
            if confirm == 'y':
                break
            else:
                print("Operation cancelled.")
                sys.exit()
        else:
            print("Invalid choice. Please enter 1 or 2.")

    print(f"\n🎯 TARGETING: {env_name} ENVIRONMENT\n")

    try:
        # Get API credentials
        headers, api_url = get_api_credentials(env_prefix)
        if not headers or not api_url:
            raise Exception("Failed to get API credentials. Check your .env file.")

        # Fetch sales orders
        if args.order_id:
            # Fetch specific order
            print(f"Fetching specific sales order: {args.order_id}")
            order_endpoint = f"{api_url}/api/resource/Sales Order/{args.order_id}"
            response = requests.get(order_endpoint, headers=headers)
            response.raise_for_status()
            order_data = response.json().get("data", {})

            if order_data.get("docstatus") != 1:
                raise Exception(f"Sales Order {args.order_id} is not submitted.")

            orders = [order_data]
            selected_order = order_data
        else:
            # Fetch and display orders for selection
            orders = fetch_submitted_sales_orders(headers, api_url, args.days)
            if not orders:
                print("No submitted sales orders found in the specified time range.")
                sys.exit(0)

            display_sales_orders(orders)
            selected_order = select_sales_order(orders)

        if not selected_order:
            print("No sales order selected. Exiting.")
            sys.exit(0)

        # Generate tasks
        tasks = generate_tasks_from_order(selected_order)

        if not tasks:
            print("No tasks generated. Exiting.")
            sys.exit(0)

        # Display summary
        display_task_summary(tasks)

        # Save tasks
        order_name = selected_order.get("name", "UNKNOWN")
        save_tasks_to_files(tasks, order_name)

        print(f"\n🚀 Task generation complete!")
        print(f"Generated {len(tasks)} tasks for Sales Order: {order_name}")
        print(f"Files saved to: {OUTPUT_DIR}")

    except Exception as e:
        print(f"\n❌ A critical error occurred: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
