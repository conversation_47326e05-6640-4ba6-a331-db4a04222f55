import os
import requests
from dotenv import load_dotenv

def fetch_erpnext_customers():
    """
    Connects to the ERPNext API and fetches a list of all Customers.
    """
    load_dotenv()
    api_url = os.getenv("ERPNEXT_API_URL")
    api_key = os.getenv("ERPNEXT_API_KEY")
    api_secret = os.getenv("ERPNEXT_API_SECRET")

    if not all([api_url, api_key, api_secret]):
        print("Error: Missing required environment variables.")
        return

    customer_endpoint = f"{api_url}/api/resource/Customer"
    headers = {
        "Authorization": f"token {api_key}:{api_secret}",
        "Accept": "application/json",
    }
    params = {
        "fields": '["name", "customer_name"]',
        "limit_page_length": 0
    }

    try:
        print(f"Attempting to fetch customers from {api_url}...")
        response = requests.get(customer_endpoint, headers=headers, params=params)
        response.raise_for_status()
        
        customers = response.json().get("data", [])

        if not customers:
            print("Successfully connected, but no customers were found.")
        else:
            print("\n--- ✅ Success! Found the following customers: ---")
            for customer in customers:
                # 'name' is the unique ID, 'customer_name' is the full name
                print(f"- Name: {customer['name']}")
            print("--------------------------------------------------\n")
            print("Note: Use the exact 'Name' from this list in your quote script.")

    except requests.exceptions.RequestException as e:
        print(f"❌ An error occurred: {e}")
        if e.response:
            print(f"Response Body: {e.response.text}")

if __name__ == "__main__":
    fetch_erpnext_customers()